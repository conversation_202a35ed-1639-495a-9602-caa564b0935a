/* CSS Root Variables */
:root {
    --primary-color: #007bff;
    --secondary-color: #6c757d;
    --success-color: #28a745;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
    --info-color: #17a2b8;
    --light-color: #f8f9fa;
    --dark-color: #343a40;
    --white-color: #ffffff;
    --border-radius: 0.5rem;
    --box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    --box-shadow-lg: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    --transition: all 0.3s ease;
    --font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* Global Styles */
body {
    font-family: var(--font-family);
    background-color: #f5f6fa;
    color: var(--dark-color);
    line-height: 1.6;
}

/* Custom Card Styles */
.card {
    border: none;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    transition: var(--transition);
    overflow: hidden;
}

.card:hover {
    box-shadow: var(--box-shadow-lg);
    transform: translateY(-2px);
}

/* Contract Type Cards */
.contract-type-card {
    cursor: pointer;
    height: 100%;
    border-left: 4px solid transparent;
    transition: var(--transition);
}

.contract-type-card:hover {
    border-left-color: var(--primary-color);
    background-color: rgba(0, 123, 255, 0.02);
}

.contract-type-card .card-body {
    padding: 1.5rem;
    display: flex;
    flex-direction: column;
    height: 100%;
}

.contract-type-icon {
    font-size: 2.5rem;
    margin-bottom: 1rem;
    opacity: 0.8;
}

.contract-type-title {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: var(--dark-color);
}

.contract-type-description {
    color: var(--secondary-color);
    font-size: 0.9rem;
    flex-grow: 1;
}

.contract-type-badge {
    align-self: flex-start;
    margin-top: 1rem;
    font-size: 0.75rem;
    padding: 0.25rem 0.75rem;
}

/* Contract Option Cards */
.contract-option-card {
    cursor: pointer;
    transition: var(--transition);
    border: 1px solid #e9ecef;
    margin-bottom: 1rem;
}

.contract-option-card:hover {
    border-color: var(--primary-color);
    box-shadow: 0 0.25rem 0.5rem rgba(0, 123, 255, 0.1);
}

.contract-option-card .card-body {
    padding: 1.25rem;
}

.option-title {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--dark-color);
    margin-bottom: 0.5rem;
}

.option-description {
    color: var(--secondary-color);
    font-size: 0.9rem;
    margin-bottom: 1rem;
}

.option-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.option-meta small {
    color: var(--secondary-color);
}

.complexity-badge {
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
}

.complexity-low {
    background-color: rgba(40, 167, 69, 0.1);
    color: var(--success-color);
    border: 1px solid rgba(40, 167, 69, 0.2);
}

.complexity-medium {
    background-color: rgba(255, 193, 7, 0.1);
    color: #856404;
    border: 1px solid rgba(255, 193, 7, 0.2);
}

.complexity-high {
    background-color: rgba(220, 53, 69, 0.1);
    color: var(--danger-color);
    border: 1px solid rgba(220, 53, 69, 0.2);
}

/* Loading Overlay */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(255, 255, 255, 0.8);
    display: none;
    justify-content: center;
    align-items: center;
    z-index: 9999;
}

.loading-overlay.show {
    display: flex;
}

/* Search and Filter Section */
.input-group-text {
    background-color: var(--white-color);
    border-color: #ced4da;
    color: var(--secondary-color);
}

.form-control:focus,
.form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

/* Modal Enhancements */
.modal-content {
    border: none;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow-lg);
}

.modal-header {
    background-color: var(--light-color);
    border-bottom: 1px solid #dee2e6;
    border-radius: var(--border-radius) var(--border-radius) 0 0;
}

.modal-title {
    font-weight: 600;
    color: var(--dark-color);
}

/* Button Enhancements */
.btn {
    border-radius: var(--border-radius);
    font-weight: 500;
    transition: var(--transition);
    border: none;
    padding: 0.5rem 1rem;
}

.btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.1);
}

.btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-primary:hover {
    background-color: #0056b3;
    border-color: #0056b3;
}

/* Navigation Enhancements */
.navbar-brand {
    font-weight: 600;
    font-size: 1.25rem;
}

.nav-link {
    font-weight: 500;
    transition: var(--transition);
}

.nav-link:hover {
    color: rgba(255, 255, 255, 0.8) !important;
}

/* Contract Details Styles */
.detail-item {
    margin-bottom: 1rem;
    padding: 1rem;
    background-color: var(--light-color);
    border-radius: var(--border-radius);
    border-left: 3px solid var(--primary-color);
}

.detail-label {
    font-weight: 600;
    color: var(--dark-color);
    margin-bottom: 0.25rem;
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.detail-value {
    color: var(--secondary-color);
    font-size: 1rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .container-fluid {
        padding-left: 1rem;
        padding-right: 1rem;
    }
    
    .contract-type-card .card-body {
        padding: 1rem;
    }
    
    .contract-option-card .card-body {
        padding: 1rem;
    }
    
    .option-meta {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .modal-dialog {
        margin: 0.5rem;
    }
}

@media (max-width: 576px) {
    .navbar-brand {
        font-size: 1rem;
    }
    
    .h3 {
        font-size: 1.5rem;
    }
    
    .contract-type-icon {
        font-size: 2rem;
    }
}

/* Animation Classes */
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.slide-in {
    animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateX(-20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* Utility Classes */
.text-truncate-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.cursor-pointer {
    cursor: pointer;
}

.border-start-primary {
    border-left: 3px solid var(--primary-color) !important;
}

.bg-light-primary {
    background-color: rgba(0, 123, 255, 0.05) !important;
}
