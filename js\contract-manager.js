/**
 * Contract Management System JavaScript
 * Handles all UI interactions and data management
 */

class ContractManager {
    constructor() {
        this.contractData = null;
        this.filteredData = null;
        this.currentContractType = null;
        this.currentContractOption = null;
        
        this.init();
    }

    /**
     * Initialize the application
     */
    async init() {
        try {
            this.showLoading(true);
            await this.loadContractData();
            this.setupEventListeners();
            this.renderContractTypes();
            this.populateFilters();
            this.showLoading(false);
        } catch (error) {
            console.error('Error initializing Contract Manager:', error);
            this.showError('Failed to load contract data. Please refresh the page.');
            this.showLoading(false);
        }
    }

    /**
     * Load contract data from JSON file
     */
    async loadContractData() {
        try {
            const response = await fetch('data/contracts.json');
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            this.contractData = await response.json();
            this.filteredData = [...this.contractData.contractTypes];
        } catch (error) {
            console.error('Error loading contract data:', error);
            throw error;
        }
    }

    /**
     * Setup event listeners
     */
    setupEventListeners() {
        // Search functionality
        const searchInput = document.getElementById('searchInput');
        searchInput.addEventListener('input', (e) => {
            this.handleSearch(e.target.value);
        });

        // Filter functionality
        const contractTypeFilter = document.getElementById('contractTypeFilter');
        const complexityFilter = document.getElementById('complexityFilter');
        
        contractTypeFilter.addEventListener('change', () => this.applyFilters());
        complexityFilter.addEventListener('change', () => this.applyFilters());

        // New contract button
        const newContractBtn = document.getElementById('newContractBtn');
        newContractBtn.addEventListener('click', () => {
            this.showContractTypes();
        });

        // Generate contract button
        const generateContractBtn = document.getElementById('generateContractBtn');
        generateContractBtn.addEventListener('click', () => {
            this.generateContract();
        });
    }

    /**
     * Show/hide loading overlay
     */
    showLoading(show) {
        const loadingOverlay = document.getElementById('loadingOverlay');
        if (show) {
            loadingOverlay.classList.add('show');
        } else {
            loadingOverlay.classList.remove('show');
        }
    }

    /**
     * Show error message
     */
    showError(message) {
        // Create a simple alert for now - could be enhanced with a toast notification
        alert(`Error: ${message}`);
    }

    /**
     * Show success message
     */
    showSuccess(message) {
        // Create a simple alert for now - could be enhanced with a toast notification
        alert(`Success: ${message}`);
    }

    /**
     * Render contract types grid
     */
    renderContractTypes() {
        const grid = document.getElementById('contractTypesGrid');
        grid.innerHTML = '';

        this.filteredData.forEach((contractType, index) => {
            const card = this.createContractTypeCard(contractType, index);
            grid.appendChild(card);
        });
    }

    /**
     * Create a contract type card element
     */
    createContractTypeCard(contractType, index) {
        const col = document.createElement('div');
        col.className = 'col-lg-4 col-md-6 mb-4';
        
        col.innerHTML = `
            <div class="card contract-type-card fade-in" data-contract-type="${contractType.id}" 
                 style="animation-delay: ${index * 0.1}s">
                <div class="card-body text-center">
                    <div class="contract-type-icon" style="color: ${contractType.color}">
                        <i class="${contractType.icon}"></i>
                    </div>
                    <h5 class="contract-type-title">${contractType.name}</h5>
                    <p class="contract-type-description text-truncate-2">${contractType.description}</p>
                    <span class="badge contract-type-badge" style="background-color: ${contractType.color}; color: white;">
                        ${contractType.options.length} Options Available
                    </span>
                </div>
            </div>
        `;

        // Add click event listener
        col.querySelector('.contract-type-card').addEventListener('click', () => {
            this.showContractOptions(contractType);
        });

        return col;
    }

    /**
     * Show contract options modal
     */
    showContractOptions(contractType) {
        this.currentContractType = contractType;
        
        // Update modal title and description
        const modalIcon = document.querySelector('.modal-icon');
        const modalTitleText = document.getElementById('modalTitleText');
        const modalDescription = document.getElementById('modalDescription');
        
        modalIcon.className = `modal-icon me-2 ${contractType.icon}`;
        modalIcon.style.color = contractType.color;
        modalTitleText.textContent = contractType.name;
        modalDescription.textContent = contractType.description;

        // Render contract options
        this.renderContractOptions(contractType.options);

        // Show modal
        const modal = new bootstrap.Modal(document.getElementById('contractOptionsModal'));
        modal.show();
    }

    /**
     * Render contract options in modal
     */
    renderContractOptions(options) {
        const grid = document.getElementById('contractOptionsGrid');
        grid.innerHTML = '';

        options.forEach((option, index) => {
            const card = this.createContractOptionCard(option, index);
            grid.appendChild(card);
        });
    }

    /**
     * Create a contract option card element
     */
    createContractOptionCard(option, index) {
        const col = document.createElement('div');
        col.className = 'col-md-6 mb-3';
        
        const complexityClass = `complexity-${option.complexity.toLowerCase()}`;
        
        col.innerHTML = `
            <div class="card contract-option-card slide-in" data-option-id="${option.id}"
                 style="animation-delay: ${index * 0.1}s">
                <div class="card-body">
                    <h6 class="option-title">${option.name}</h6>
                    <p class="option-description">${option.description}</p>
                    <div class="option-meta">
                        <small><i class="fas fa-clock me-1"></i>${option.duration}</small>
                        <span class="complexity-badge ${complexityClass}">
                            ${option.complexity} Complexity
                        </span>
                    </div>
                </div>
            </div>
        `;

        // Add click event listener
        col.querySelector('.contract-option-card').addEventListener('click', () => {
            this.showContractDetails(option);
        });

        return col;
    }

    /**
     * Show contract details modal
     */
    showContractDetails(option) {
        this.currentContractOption = option;
        
        // Render contract details
        this.renderContractDetails(option);

        // Hide options modal and show details modal
        const optionsModal = bootstrap.Modal.getInstance(document.getElementById('contractOptionsModal'));
        optionsModal.hide();

        setTimeout(() => {
            const detailsModal = new bootstrap.Modal(document.getElementById('contractDetailsModal'));
            detailsModal.show();
        }, 300);
    }

    /**
     * Render contract details
     */
    renderContractDetails(option) {
        const content = document.getElementById('contractDetailsContent');
        
        content.innerHTML = `
            <div class="row">
                <div class="col-12">
                    <div class="detail-item">
                        <div class="detail-label">Contract Name</div>
                        <div class="detail-value">${option.name}</div>
                    </div>
                    
                    <div class="detail-item">
                        <div class="detail-label">Description</div>
                        <div class="detail-value">${option.description}</div>
                    </div>
                    
                    <div class="detail-item">
                        <div class="detail-label">Contract Type</div>
                        <div class="detail-value">${this.currentContractType.name}</div>
                    </div>
                    
                    <div class="detail-item">
                        <div class="detail-label">Expected Duration</div>
                        <div class="detail-value">${option.duration}</div>
                    </div>
                    
                    <div class="detail-item">
                        <div class="detail-label">Complexity Level</div>
                        <div class="detail-value">
                            <span class="complexity-badge ${`complexity-${option.complexity.toLowerCase()}`}">
                                ${option.complexity}
                            </span>
                        </div>
                    </div>
                    
                    <div class="detail-item">
                        <div class="detail-label">Template File</div>
                        <div class="detail-value">
                            <i class="fas fa-file-word me-2"></i>${option.template}
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * Generate contract (placeholder functionality)
     */
    generateContract() {
        if (!this.currentContractOption) {
            this.showError('No contract option selected.');
            return;
        }

        // Simulate contract generation
        this.showLoading(true);
        
        setTimeout(() => {
            this.showLoading(false);
            this.showSuccess(`Contract "${this.currentContractOption.name}" has been generated successfully!`);
            
            // Close modal
            const modal = bootstrap.Modal.getInstance(document.getElementById('contractDetailsModal'));
            modal.hide();
        }, 2000);
    }

    /**
     * Handle search functionality
     */
    handleSearch(searchTerm) {
        if (!searchTerm.trim()) {
            this.filteredData = [...this.contractData.contractTypes];
        } else {
            const term = searchTerm.toLowerCase();
            this.filteredData = this.contractData.contractTypes.filter(contractType => {
                return contractType.name.toLowerCase().includes(term) ||
                       contractType.description.toLowerCase().includes(term) ||
                       contractType.options.some(option => 
                           option.name.toLowerCase().includes(term) ||
                           option.description.toLowerCase().includes(term)
                       );
            });
        }
        
        this.applyFilters();
    }

    /**
     * Apply filters
     */
    applyFilters() {
        const contractTypeFilter = document.getElementById('contractTypeFilter').value;
        const complexityFilter = document.getElementById('complexityFilter').value;
        
        let filtered = [...this.filteredData];
        
        if (contractTypeFilter) {
            filtered = filtered.filter(contractType => contractType.id === contractTypeFilter);
        }
        
        if (complexityFilter) {
            filtered = filtered.filter(contractType => 
                contractType.options.some(option => option.complexity === complexityFilter)
            );
        }
        
        this.filteredData = filtered;
        this.renderContractTypes();
    }

    /**
     * Populate filter dropdowns
     */
    populateFilters() {
        const contractTypeFilter = document.getElementById('contractTypeFilter');
        
        this.contractData.contractTypes.forEach(contractType => {
            const option = document.createElement('option');
            option.value = contractType.id;
            option.textContent = contractType.name;
            contractTypeFilter.appendChild(option);
        });
    }

    /**
     * Show contract types (reset view)
     */
    showContractTypes() {
        // Reset filters
        document.getElementById('searchInput').value = '';
        document.getElementById('contractTypeFilter').value = '';
        document.getElementById('complexityFilter').value = '';
        
        this.filteredData = [...this.contractData.contractTypes];
        this.renderContractTypes();
    }
}

// Initialize the Contract Manager when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new ContractManager();
});
